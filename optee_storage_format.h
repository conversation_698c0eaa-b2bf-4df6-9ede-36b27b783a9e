/* SPDX-License-Identifier: BSD-2-Clause */
/*
 * OPTEE Storage File Format Data Structure Design
 *
 * 这个头文件定义了OPTEE存储文件的完整数据结构格式，
 * 包含GP标准头部、属性数据和用户数据的布局。
 */

#ifndef __OPTEE_STORAGE_FORMAT_H
#define __OPTEE_STORAGE_FORMAT_H

#include <stdint.h>

/*
 * OPTEE存储文件格式总览:
 *
 * +================================+
 * | GP标准头部 (24字节)             |  <- tee_svc_storage_head
 * +================================+
 * | 序列化属性数据 (可变长度)        |  <- 对象属性
 * +================================+  <- ds_pos 指向这里
 * | 用户数据 (可变长度)             |  <- 实际存储的数据
 * +================================+
 *
 * 对应关系:
 * - GP头部 ↔ tee_svc_storage_head
 * - 属性数据 ↔ tee_obj.attr 指向的内存
 * - 用户数据偏移 ↔ tee_obj.ds_pos
 * - 文件句柄 ↔ tee_obj.fh
 */

/* TEE对象类型定义 (示例) */
#define TEE_TYPE_DATA           0x00000000
#define TEE_TYPE_AES            0xA0000010
#define TEE_TYPE_RSA_PUBLIC_KEY 0xA0000030

/* TEE属性ID定义 (示例) */
#define TEE_ATTR_SECRET_VALUE   0xC0000001  /* 引用属性 */
#define TEE_ATTR_KEY_USAGE      0xF0000001  /* 值属性 */
#define TEE_ATTR_ALGORITHM      0xF0000002  /* 值属性 */

/* 属性类型标志 */
#define TEE_ATTR_FLAG_VALUE     0x20000000

/*
 * GP Storage Header - Fixed 24 bytes
 * This corresponds to struct tee_svc_storage_head in OPTEE
 */
struct optee_storage_gp_header {
    uint32_t attr_size;         /* Size of attributes section */
    uint32_t objectSize;        /* Current object size */
    uint32_t maxObjectSize;     /* Maximum object size */
    uint32_t objectUsage;       /* Object usage flags */
    uint32_t objectType;        /* TEE object type */
    uint32_t have_attrs;        /* Bitfield of present attributes */
} __attribute__((packed));

/*
 * Extended File Header - Additional metadata
 */
struct optee_storage_extended_header {
    uint32_t format_version;    /* File format version */
    uint32_t header_size;       /* Total header size */
    uint32_t data_offset;       /* Offset to user data (ds_pos) */
    uint32_t total_file_size;   /* Total file size */
    uint32_t checksum;          /* Header checksum (optional) */
    uint32_t flags;             /* Additional flags */
    uint64_t creation_time;     /* Creation timestamp */
    uint64_t modification_time; /* Last modification timestamp */
    TEE_UUID owner_uuid;        /* Owner TA UUID */
    uint8_t object_id_len;      /* Object ID length */
    uint8_t reserved[3];        /* Padding for alignment */
    uint8_t object_id[OPTEE_STORAGE_MAX_OBJECT_ID_LEN]; /* Object ID */
} __attribute__((packed));

/*
 * Complete File Header
 */
struct optee_storage_file_header {
    struct optee_storage_gp_header gp_header;
    struct optee_storage_extended_header ext_header;
} __attribute__((packed));

/*
 * Attribute Entry - Variable size
 */
struct optee_storage_attr_entry {
    uint32_t attribute_id;      /* TEE attribute ID */
    uint32_t attr_type;         /* Value or Reference */
    uint32_t data_size;         /* Size of attribute data */
    uint32_t reserved;          /* Padding for alignment */
    /* Followed by attribute data */
    uint8_t data[];             /* Variable size data */
} __attribute__((packed));

/*
 * Attributes Section Header
 */
struct optee_storage_attr_header {
    uint32_t attr_count;        /* Number of attributes */
    uint32_t total_size;        /* Total size of attributes section */
    uint32_t checksum;          /* Attributes checksum (optional) */
    uint32_t reserved;          /* Padding */
} __attribute__((packed));

/*
 * User Data Section Header
 */
struct optee_storage_data_header {
    uint32_t data_size;         /* Size of user data */
    uint32_t data_position;     /* Current position in data */
    uint32_t checksum;          /* Data checksum (optional) */
    uint32_t compression;       /* Compression type (if any) */
} __attribute__((packed));

/*
 * Complete Storage File Structure
 */
struct optee_storage_file {
    /* File header */
    struct optee_storage_file_header header;
    
    /* Attributes section */
    struct optee_storage_attr_header attr_header;
    /* Followed by variable number of optee_storage_attr_entry */
    
    /* User data section */
    struct optee_storage_data_header data_header;
    /* Followed by user data */
} __attribute__((packed));

/*
 * Helper structure for attribute parsing
 */
struct optee_storage_parsed_attr {
    uint32_t attribute_id;
    uint32_t attr_type;
    union {
        struct {
            uint32_t a;
            uint32_t b;
        } value;
        struct {
            void *buffer;
            size_t length;
        } ref;
    } content;
};

/*
 * File layout calculation helpers
 */
struct optee_storage_layout {
    size_t header_offset;       /* Always 0 */
    size_t header_size;         /* Size of complete header */
    size_t attr_offset;         /* Offset to attributes section */
    size_t attr_size;           /* Size of attributes section */
    size_t data_offset;         /* Offset to user data (ds_pos) */
    size_t data_size;           /* Size of user data */
    size_t total_size;          /* Total file size */
};

/*
 * File creation parameters
 */
struct optee_storage_create_params {
    TEE_UUID owner_uuid;
    const char *object_id;
    size_t object_id_len;
    uint32_t object_type;
    uint32_t max_object_size;
    uint32_t object_usage;
    uint32_t flags;
    const struct optee_storage_parsed_attr *attrs;
    size_t attr_count;
    const void *initial_data;
    size_t initial_data_size;
};

/*
 * Function prototypes for file format operations
 */

/* Calculate file layout offsets and sizes */
int optee_storage_calculate_layout(const struct optee_storage_create_params *params,
                                   struct optee_storage_layout *layout);

/* Create a new storage file structure */
int optee_storage_create_file(const struct optee_storage_create_params *params,
                              struct optee_storage_file **file_out,
                              size_t *file_size_out);

/* Parse an existing storage file */
int optee_storage_parse_file(const void *file_data, size_t file_size,
                             struct optee_storage_file **parsed_file);

/* Get attribute by ID */
int optee_storage_get_attribute(const struct optee_storage_file *file,
                                uint32_t attr_id,
                                struct optee_storage_parsed_attr *attr_out);

/* Serialize attributes to binary format */
int optee_storage_serialize_attributes(const struct optee_storage_parsed_attr *attrs,
                                       size_t attr_count,
                                       void **attr_data_out,
                                       size_t *attr_size_out);

/* Deserialize attributes from binary format */
int optee_storage_deserialize_attributes(const void *attr_data, size_t attr_size,
                                          struct optee_storage_parsed_attr **attrs_out,
                                          size_t *attr_count_out);

/* Validate file format and integrity */
int optee_storage_validate_file(const struct optee_storage_file *file, size_t file_size);

/* Get user data pointer and size */
int optee_storage_get_user_data(const struct optee_storage_file *file,
                                const void **data_out, size_t *size_out);

/* Update user data in file */
int optee_storage_update_user_data(struct optee_storage_file *file,
                                   const void *new_data, size_t new_size,
                                   size_t offset);

/* Free allocated file structure */
void optee_storage_free_file(struct optee_storage_file *file);

/* Free allocated attributes */
void optee_storage_free_attributes(struct optee_storage_parsed_attr *attrs, size_t count);

/*
 * Utility macros for offset calculations
 */
#define OPTEE_STORAGE_GP_HEADER_SIZE        sizeof(struct optee_storage_gp_header)
#define OPTEE_STORAGE_EXT_HEADER_SIZE       sizeof(struct optee_storage_extended_header)
#define OPTEE_STORAGE_FULL_HEADER_SIZE      sizeof(struct optee_storage_file_header)
#define OPTEE_STORAGE_ATTR_HEADER_SIZE      sizeof(struct optee_storage_attr_header)
#define OPTEE_STORAGE_DATA_HEADER_SIZE      sizeof(struct optee_storage_data_header)

/* Calculate minimum file size */
#define OPTEE_STORAGE_MIN_FILE_SIZE \
    (OPTEE_STORAGE_FULL_HEADER_SIZE + OPTEE_STORAGE_ATTR_HEADER_SIZE + OPTEE_STORAGE_DATA_HEADER_SIZE)

/* Alignment macros */
#define OPTEE_STORAGE_ALIGN_SIZE            8
#define OPTEE_STORAGE_ALIGN(x)              (((x) + OPTEE_STORAGE_ALIGN_SIZE - 1) & ~(OPTEE_STORAGE_ALIGN_SIZE - 1))

/*
 * Error codes
 */
#define OPTEE_STORAGE_OK                    0
#define OPTEE_STORAGE_ERROR_INVALID_PARAM   -1
#define OPTEE_STORAGE_ERROR_NO_MEMORY       -2
#define OPTEE_STORAGE_ERROR_INVALID_FORMAT  -3
#define OPTEE_STORAGE_ERROR_CHECKSUM        -4
#define OPTEE_STORAGE_ERROR_TOO_LARGE       -5
#define OPTEE_STORAGE_ERROR_NOT_FOUND       -6

#endif /* __OPTEE_STORAGE_FORMAT_H */
